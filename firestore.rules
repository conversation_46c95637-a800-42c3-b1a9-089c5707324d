rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Fix: Change 'opinions' to 'dailyOpinions' to match your actual collection
    match /dailyOpinions/{opinionId} {
      allow read: if true;
      allow write: if false;
    }
    
    // Add missing collections:
    match /archivedResponses/{document} {
      allow read: if true;
      allow write: if false;
    }
    
    match /opinionStats/{document} {
      allow read: if true;
      allow write: if false;
    }
    
    match /userSummaries/{document} {
      allow read: if true;
      allow write: if false;
    }
    
    // Keep your existing rules:
    match /responses/{responseId} {
      allow read: if true;
      allow create: if true;
      allow update, delete: if false;
    }
    
    match /diy_opinions/{opinionId} {
      allow read: if true;
      allow create: if true;
      allow update, delete: if false;
    }
    
    match /diy_votes/{voteId} {
      allow read: if true;
      allow create: if true;
      allow update, delete: if false;
    }
    
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /feedback/{feedbackId} {
      allow read: if false;
      allow create: if true;
      allow update, delete: if false;
    }
    
    match /{document=**} {
      allow read, write: if false;
    }
  }
}