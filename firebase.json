{"hosting": {"public": "out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/sitemap.xml", "function": "sitemap"}, {"source": "**", "destination": "/index.html"}]}, "functions": {"source": "functions"}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5005}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}}