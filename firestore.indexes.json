{"indexes": [{"collectionGroup": "responses", "queryScope": "COLLECTION", "fields": [{"fieldPath": "opinionId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "diy_votes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "opinionId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "diy_opinions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "shareableToken", "order": "ASCENDING"}]}], "fieldOverrides": []}